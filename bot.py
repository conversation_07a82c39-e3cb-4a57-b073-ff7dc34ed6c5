import discord
import asyncio
import aiohttp
from datetime import datetime, timedelta
import pytz
from typing import Dict, Optional, Any
import os
import signal
import sys
from dotenv import load_dotenv
from enum import Enum

# Load environment variables
load_dotenv()

# Bot setup
intents = discord.Intents.default()
intents.message_content = True
bot = discord.Bot(intents=intents)

# Constants
IRAN_TIMEZONE = pytz.timezone('Asia/Tehran')
GW2_API_BASE = "https://api.guildwars2.com/v2"
GW2_WORLD_BOSSES = f"{GW2_API_BASE}/worldbosses"
GW2_EVENTS = f"{GW2_API_BASE}/events"
GW2_MAPS = f"{GW2_API_BASE}/maps"

# Cache for boss data and countdown settings
boss_cache = {}
active_countdowns = {}  # {message_id: {'boss_id': str, 'channel_id': int, 'next_spawn': datetime}}
last_fetch = None
CACHE_DURATION = 300  # 5 minutes in seconds
COUNTDOWN_UPDATE_INTERVAL = 30  # seconds between countdown updates

# ═══════════════════════════════════════════════════════════════════════════════
# 🎨 VISUAL ENHANCEMENT SYSTEM
# ═══════════════════════════════════════════════════════════════════════════════

# Boss-specific emoji and theming system
BOSS_THEMES = {
    # Core Tyria Dragons & Major Bosses
    "shatterer": {
        "emoji": "🐉", "element": "⚡", "color": 0x9932CC, "type": "Dragon",
        "theme_emojis": ["⚡", "💎", "🔮", "⛰️"]
    },
    "tequatl": {
        "emoji": "🐲", "element": "💀", "color": 0x8B0000, "type": "Undead Dragon",
        "theme_emojis": ["💀", "🌊", "⚰️", "🦴"]
    },
    "claw_of_jormag": {
        "emoji": "🧊", "element": "❄️", "color": 0x87CEEB, "type": "Ice Dragon",
        "theme_emojis": ["❄️", "🧊", "🌨️", "⛄"]
    },

    # Elementals
    "megadestroyer": {
        "emoji": "🌋", "element": "🔥", "color": 0xFF4500, "type": "Fire Elemental",
        "theme_emojis": ["🔥", "🌋", "💥", "🔴"]
    },
    "fire_elemental": {
        "emoji": "🔥", "element": "🔥", "color": 0xFF6347, "type": "Fire Elemental",
        "theme_emojis": ["🔥", "🌶️", "🟠", "💥"]
    },

    # Mechanical/Golem Bosses
    "golem_mark_ii": {
        "emoji": "🤖", "element": "⚙️", "color": 0x708090, "type": "Mechanical",
        "theme_emojis": ["🤖", "⚙️", "🔧", "⚡"]
    },

    # Nature/Jungle Bosses
    "great_jungle_wurm": {
        "emoji": "🐛", "element": "🌿", "color": 0x228B22, "type": "Nature Beast",
        "theme_emojis": ["🐛", "🌿", "🍃", "🌳"]
    },

    # Shadow/Dark Bosses
    "shadow_behemoth": {
        "emoji": "👻", "element": "🌑", "color": 0x2F2F2F, "type": "Shadow Entity",
        "theme_emojis": ["👻", "🌑", "💀", "🖤"]
    },

    # Ice/Frost Bosses
    "svanir_shaman_chief": {
        "emoji": "🧙‍♂️", "element": "❄️", "color": 0x4682B4, "type": "Ice Shaman",
        "theme_emojis": ["🧙‍♂️", "❄️", "🔮", "⛄"]
    },

    # Pirate/Naval Bosses
    "taidha_covington": {
        "emoji": "🏴‍☠️", "element": "⚔️", "color": 0x8B4513, "type": "Pirate Captain",
        "theme_emojis": ["🏴‍☠️", "⚔️", "💰", "🦜"]
    },

    # Expansion Bosses
    "chak_gerent": {
        "emoji": "🕷️", "element": "🟤", "color": 0x8B4513, "type": "Insectoid",
        "theme_emojis": ["🕷️", "🟤", "🕸️", "🦗"]
    },
    "death_branded_shatterer": {
        "emoji": "💜", "element": "🔮", "color": 0x9400D3, "type": "Branded Dragon",
        "theme_emojis": ["💜", "🔮", "⚡", "🌟"]
    },
    "drakkar": {
        "emoji": "🐉", "element": "🧊", "color": 0x191970, "type": "Elder Dragon",
        "theme_emojis": ["🐉", "🧊", "❄️", "🌊"]
    },
    "dragonstorm": {
        "emoji": "⛈️", "element": "⚡", "color": 0xFF1493, "type": "Dragon Storm",
        "theme_emojis": ["⛈️", "⚡", "🌪️", "🐉"]
    },
    "soo_won": {
        "emoji": "🌊", "element": "💧", "color": 0x00CED1, "type": "Elder Dragon",
        "theme_emojis": ["🌊", "💧", "🐉", "🌀"]
    },
    "aetherblade_assault": {
        "emoji": "🚁", "element": "⚡", "color": 0xFF69B4, "type": "Airship",
        "theme_emojis": ["🚁", "⚡", "💥", "🔫"]
    },

    # Special Event Bosses
    "karka_queen": {
        "emoji": "🦀", "element": "🌊", "color": 0x20B2AA, "type": "Crustacean",
        "theme_emojis": ["🦀", "🌊", "🏖️", "🐚"]
    },
    "marionette": {
        "emoji": "🎭", "element": "⚙️", "color": 0x800080, "type": "Mechanical",
        "theme_emojis": ["🎭", "⚙️", "🎪", "🔧"]
    },
    "triple_trouble": {
        "emoji": "🐍", "element": "💀", "color": 0x556B2F, "type": "Wurm",
        "theme_emojis": ["🐍", "💀", "🌿", "🦴"]
    }
}

# Notification type colors and styling
NOTIFICATION_STYLES = {
    "10_min": {
        "color": 0xFFA500,  # Orange
        "urgency": "⚠️",
        "title_prefix": "⏰ INCOMING",
        "border": "🟠"
    },
    "5_min": {
        "color": 0xFF4500,  # Red-Orange
        "urgency": "🚨",
        "title_prefix": "🚨 URGENT",
        "border": "🔴"
    },
    "spawn": {
        "color": 0x00FF32,  # Bright Green
        "urgency": "🎯",
        "title_prefix": "🎯 LIVE NOW",
        "border": "🟢"
    },
    "info": {
        "color": 0x1E90FF,  # Dodger Blue
        "urgency": "ℹ️",
        "title_prefix": "📋 INFO",
        "border": "🔵"
    }
}

# ═══════════════════════════════════════════════════════════════════════════════
# 🛠️ VISUAL ENHANCEMENT UTILITIES
# ═══════════════════════════════════════════════════════════════════════════════

def get_boss_theme(boss_id: str) -> Dict[str, Any]:
    """Get the visual theme for a specific boss"""
    return BOSS_THEMES.get(boss_id, {
        "emoji": "⚔️", "element": "⭐", "color": 0x808080, "type": "Unknown",
        "theme_emojis": ["⚔️", "⭐", "🎯", "💫"]
    })

def get_notification_style(notification_type: str) -> Dict[str, Any]:
    """Get the style configuration for a notification type"""
    return NOTIFICATION_STYLES.get(notification_type, NOTIFICATION_STYLES["info"])

def create_enhanced_progress_bar(progress: float, length: int = 20, style: str = "default") -> str:
    """Create an enhanced progress bar with different visual styles"""
    filled_length = int(progress * length)

    if style == "fire":
        filled_char = "🔥"
        empty_char = "⬛"
    elif style == "ice":
        filled_char = "🧊"
        empty_char = "⬜"
    elif style == "electric":
        filled_char = "⚡"
        empty_char = "⬛"
    elif style == "nature":
        filled_char = "🌿"
        empty_char = "🟫"
    elif style == "shadow":
        filled_char = "🖤"
        empty_char = "⬜"
    elif style == "urgent":
        filled_char = "🟥"
        empty_char = "⬛"
    elif style == "warning":
        filled_char = "🟨"
        empty_char = "⬛"
    else:  # default
        filled_char = "🟩"
        empty_char = "⬛"

    return filled_char * filled_length + empty_char * (length - filled_length)

def create_visual_separator(style: str = "default") -> str:
    """Create visual separators for embeds"""
    separators = {
        "default": "▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬",
        "fancy": "◆◇◆◇◆◇◆◇◆◇◆◇◆◇◆◇◆◇◆◇",
        "stars": "⭐✨⭐✨⭐✨⭐✨⭐✨⭐✨⭐✨⭐✨⭐✨⭐",
        "dragons": "🐉━━━━━━━━━━━━━━━━━━━━🐉",
        "swords": "⚔️━━━━━━━━━━━━━━━━━━━━⚔️",
        "gems": "💎◆◇◆◇◆◇◆◇◆◇◆◇◆◇◆◇◆💎"
    }
    return separators.get(style, separators["default"])

def format_countdown_display(time_until: timedelta, boss_theme: Dict[str, Any], urgency_level: str) -> str:
    """Create an enhanced countdown display with visual elements"""
    hours, remainder = divmod(int(time_until.total_seconds()), 3600)
    minutes, seconds = divmod(remainder, 60)

    # Choose progress bar style based on boss theme
    if "fire" in boss_theme.get("type", "").lower() or "🔥" in boss_theme.get("theme_emojis", []):
        bar_style = "fire"
    elif "ice" in boss_theme.get("type", "").lower() or "❄️" in boss_theme.get("theme_emojis", []):
        bar_style = "ice"
    elif "electric" in boss_theme.get("type", "").lower() or "⚡" in boss_theme.get("theme_emojis", []):
        bar_style = "electric"
    elif "nature" in boss_theme.get("type", "").lower() or "🌿" in boss_theme.get("theme_emojis", []):
        bar_style = "nature"
    elif "shadow" in boss_theme.get("type", "").lower() or "🖤" in boss_theme.get("theme_emojis", []):
        bar_style = "shadow"
    elif urgency_level == "urgent":
        bar_style = "urgent"
    elif urgency_level == "warning":
        bar_style = "warning"
    else:
        bar_style = "default"

    # Create progress bar (1 hour = 100%)
    progress = max(0, min(1, 1 - (time_until.total_seconds() / 3600)))
    progress_bar = create_enhanced_progress_bar(progress, 15, bar_style)

    # Format time display with emojis
    time_str = f"⏰ **{hours:02d}:{minutes:02d}:{seconds:02d}**"

    # Add urgency indicators
    if urgency_level == "urgent":
        time_str = f"🚨 {time_str} 🚨"
    elif urgency_level == "warning":
        time_str = f"⚠️ {time_str} ⚠️"

    return f"{time_str}\n{progress_bar}"

def create_boss_title(boss: 'WorldBoss', minutes_until: int, notification_style: Dict[str, Any], boss_theme: Dict[str, Any]) -> str:
    """Create an enhanced title for boss notifications"""
    boss_emoji = boss_theme.get("emoji", "⚔️")
    element_emoji = boss_theme.get("element", "⭐")
    urgency_emoji = notification_style.get("urgency", "ℹ️")
    title_prefix = notification_style.get("title_prefix", "INFO")

    if minutes_until <= 0:
        return f"{urgency_emoji} {boss_emoji} **{boss.name}** {element_emoji} IS LIVE! {boss_emoji}"
    else:
        return f"{urgency_emoji} {title_prefix} {boss_emoji} **{boss.name}** {element_emoji} in **{minutes_until}** min!"

class BossState(Enum):
    INACTIVE = "Inactive"
    WARMUP = "Warmup"
    ACTIVE = "Active"
    SUCCESS = "Success"
    FAIL = "Fail"

class WorldBoss:
    def __init__(self, boss_id: str, name: str, waypoint: str, location: str, event_id: str):
        self.id = boss_id
        self.name = name
        self.waypoint = waypoint
        self.location = location
        self.event_id = event_id
        self.state = BossState.INACTIVE
        self.next_spawn = None
        self.last_spawn = None
        self.notified_10min = False
        self.notified_5min = False
        self.notified_spawn = False

    def update_state(self, state: BossState):
        self.state = state
        
    def update_spawn_time(self, next_spawn: datetime):
        self.next_spawn = next_spawn
        self.notified_10min = False
        self.notified_5min = False
        self.notified_spawn = False
        self.countdown_message = None

# Known world bosses with their event IDs and waypoints
WORLD_BOSSES = {
    # Core Tyria World Bosses
    "shatterer": {
        "name": "The Shatterer",
        "waypoint": "[&BN4DAAA=]",
        "location": "Blazeridge Steppes",
        "event_id": "EED8A207-FE0B-4F3F-9222-9F79B199A216"
    },
    "tequatl": {
        "name": "Tequatl the Sunless",
        "waypoint": "[&Bk8FAAA=]",
        "location": "Sparksfly Fen",
        "event_id": "D31C6041-0D49-444B-93D3-2943B46816A9"
    },
    "claw_of_jormag": {
        "name": "Claw of Jormag",
        "waypoint": "[&BHMHAAA=]",
        "location": "Frostgorge Sound",
        "event_id": "E84FF6AC-68DA-4F42-B0B7-494B6B7C4EA0"
    },
    "megadestroyer": {
        "name": "The Megadestroyer",
        "waypoint": "[&BH4CAAA=]",
        "location": "Mount Maelstrom",
        "event_id": "A3D8A207-FE0B-4F3F-9222-9F79B199A216"
    },
    "golem_mark_ii": {
        "name": "Golem Mark II",
        "waypoint": "[&BEgDAAA=]",
        "location": "Mount Maelstrom",
        "event_id": "6D2F27C8-6E5B-4B3C-8E7D-9A2A3B9B7C6D"
    },
    "fire_elemental": {
        "name": "Fire Elemental",
        "waypoint": "[&BPcAAAA=]",
        "location": "Metrica Province",
        "event_id": "D3D2E9ED-45E4-4A70-9661-6166F0EFBD91"
    },
    "great_jungle_wurm": {
        "name": "Great Jungle Wurm",
        "waypoint": "[&BPgAAAA=]",
        "location": "Caledon Forest",
        "event_id": "1A2DEE29-9D92-4F97-8B2D-2E3D1F9F14E8"
    },
    "shadow_behemoth": {
        "name": "Shadow Behemoth",
        "waypoint": "[&BPcAAAA=]",
        "location": "Queensdale",
        "event_id": "F530006C-4F2F-4FCD-8E6D-2AA0B5B25E20"
    },
    "svanir_shaman_chief": {
        "name": "Svanir Shaman Chief",
        "waypoint": "[&BKgBAAA=]",
        "location": "Wayfarer Foothills",
        "event_id": "F530006C-4F2F-4FCD-8E6D-2AA0B5B25E21"
    },
    "taidha_covington": {
        "name": "Taidha Covington",
        "waypoint": "[&BKgCAAA=]",
        "location": "Bloodtide Coast",
        "event_id": "F530006C-4F2F-4FCD-8E6D-2AA0B5B25E22"
    },
    # HoT and PoF World Bosses
    "chak_gerent": {
        "name": "Chak Gerent",
        "waypoint": "[&BL0HAAA=]",
        "location": "Tangled Depths",
        "event_id": "A4B6C3D4-E5F6-47A8-B9C0-D1E2F3A4B5C6"
    },
    "death_branded_shatterer": {
        "name": "Death-Branded Shatterer",
        "waypoint": "[&BKgIAAA=]",
        "location": "Domain of Vabbi",
        "event_id": "B5C6D7E8-F9A0-41B2-C3D4-E5F647A8B9C0"
    },
    "drakkar": {
        "name": "Drakkar",
        "waypoint": "[&BLYJAAA=]",
        "location": "Bjora Marches",
        "event_id": "C6D7E8F9-0A1B-2C3D-4E5F-647A8B9C0D1E"
    },
    # Dragon Response Missions
    "dragonstorm": {
        "name": "Dragonstorm",
        "waypoint": "[&BLwJAAA=]",
        "location": "Eye of the North",
        "event_id": "D7E8F90A-1B2C-3D4E-5F64-7A8B9C0D1E2F"
    },
    # EoD World Bosses
    "soo_won": {
        "name": "Soo-Won",
        "waypoint": "[&BL0KAAA=]",
        "location": "Seitung Province",
        "event_id": "E8F90A1B-2C3D-4E5F-647A-8B9C0D1E2F30"
    },
    "aetherblade_assault": {
        "name": "Aetherblade Assault",
        "waypoint": "[&BL0LAAA=]",
        "location": "New Kaineng City",
        "event_id": "F90A1B2C-3D4E-5F64-7A8B-9C0D1E2F3041"
    },
    # Other Notable Bosses
    "karka_queen": {
        "name": "Karka Queen",
        "waypoint": "[&BKgGAAA=]",
        "location": "Southsun Cove",
        "event_id": "0A1B2C3D-4E5F-647A-8B9C-0D1E2F304152"
    },
    "marionette": {
        "name": "Twisted Marionette",
        "waypoint": "[&BL0MAAA=]",
        "location": "Lornar's Pass",
        "event_id": "1B2C3D4E-5F64-7A8B-9C0D-1E2F30415263"
    },
    "triple_trouble": {
        "name": "Triple Trouble (Tequatl's Abomination)",
        "waypoint": "[&BKgBAAA=]",
        "location": "Bloodtide Coast",
        "event_id": "2C3D4E5F-647A-8B9C-0D1E-2F3041526374"
    }
}

async def fetch_world_boss_schedule() -> Dict[str, Any]:
    """Fetch the world boss schedule from GW2 API"""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{GW2_WORLD_BOSSES}") as response:
                if response.status == 200:
                    return await response.json()
                return {}
    except Exception as e:
        print(f"Error fetching world boss schedule: {e}")
        return {}

async def fetch_event_state(event_id: str) -> Dict[str, Any]:
    """Fetch the current state of a specific event"""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{GW2_EVENTS}/{event_id}") as response:
                if response.status == 200:
                    return await response.json()
                return {}
    except Exception as e:
        print(f"Error fetching event state: {e}")
        return {}

def calculate_next_spawn(boss_name: str) -> Optional[datetime]:
    """Calculate the next spawn time for a boss based on current time"""
    now = datetime.now(IRAN_TIMEZONE)
    
    # This is a simplified calculation - in a real implementation, you'd want to
    # use the actual schedule from the API or a more sophisticated calculation
    # based on the boss's known spawn patterns
    
    # For now, we'll use a simple 2-hour rotation for demonstration
    next_spawn = now.replace(second=0, microsecond=0)
    next_spawn = next_spawn + timedelta(hours=2 - (now.hour % 2), minutes=0)
    return next_spawn

async def check_boss_timers():
    """Check boss timers and send notifications"""
    global last_fetch, boss_cache
    
    now = datetime.now(IRAN_TIMEZONE)
    
    # Only fetch from API if cache is expired
    if not last_fetch or (now - last_fetch).total_seconds() > CACHE_DURATION:
        for boss_id, boss_data in WORLD_BOSSES.items():
            event_data = await fetch_event_state(boss_data["event_id"])
            
            if event_data and "state" in event_data:
                if boss_id not in boss_cache:
                    boss_cache[boss_id] = WorldBoss(
                        boss_id,
                        boss_data["name"],
                        boss_data["waypoint"],
                        boss_data["location"],
                        boss_data["event_id"]
                    )
                
                boss = boss_cache[boss_id]
                boss.update_state(BossState(event_data["state"]))
                
                # If boss is inactive, calculate next spawn
                if boss.state == BossState.INACTIVE and not boss.next_spawn:
                    next_spawn = calculate_next_spawn(boss_id)
                    boss.update_spawn_time(next_spawn)
        
        last_fetch = now
    
    # Check for notifications
    for boss_id, boss in list(boss_cache.items()):
        if boss.next_spawn:
            time_until = (boss.next_spawn - now).total_seconds()
            minutes_until = int(time_until / 60)
            
            # 10-minute warning
            if 600 <= time_until < 660 and not boss.notified_10min:
                for guild in bot.guilds:
                    for channel in guild.text_channels:
                        if channel.permissions_for(guild.me).send_messages:
                            await send_boss_notification(boss, channel, 10)
                            boss.notified_10min = True
                            break
            
            # 5-minute warning
            elif 300 <= time_until < 360 and not boss.notified_5min:
                for guild in bot.guilds:
                    for channel in guild.text_channels:
                        if channel.permissions_for(guild.me).send_messages:
                            await send_boss_notification(boss, channel, 5)
                            boss.notified_5min = True
                            break
            
            # Spawn time
            elif 0 <= time_until < 60 and not boss.notified_spawn:
                for guild in bot.guilds:
                    for channel in guild.text_channels:
                        if channel.permissions_for(guild.me).send_messages:
                            await send_boss_notification(boss, channel, 0)
                            boss.notified_spawn = True
                            break
                
                # Schedule next spawn
                next_spawn = calculate_next_spawn(boss_id)
                boss.update_spawn_time(next_spawn)
                
                # Clean up countdown messages for this boss
                for msg_id in list(active_countdowns.keys()):
                    if active_countdowns[msg_id]['boss_id'] == boss_id:
                        del active_countdowns[msg_id]

async def update_countdown_messages():
    """Update all active countdown messages"""
    now = datetime.now(IRAN_TIMEZONE)
    
    for message_id, data in list(active_countdowns.items()):
        try:
            channel = bot.get_channel(data['channel_id'])
            if not channel:
                del active_countdowns[message_id]
                continue
                
            message = await channel.fetch_message(message_id)
            if not message:
                del active_countdowns[message_id]
                continue
                
            boss_id = data['boss_id']
            boss = boss_cache.get(boss_id)
            
            if not boss or not boss.next_spawn or (boss.next_spawn - now).total_seconds() <= 0:
                try:
                    await message.delete()
                except:
                    pass
                del active_countdowns[message_id]
                continue
                
            minutes_until = int((boss.next_spawn - now).total_seconds() / 60)
            embed = create_boss_embed(boss, minutes_until)
            await message.edit(embed=embed)
            
        except Exception as e:
            print(f"Error updating countdown: {e}")
            del active_countdowns[message_id]

async def countdown_loop():
    """Background task to update countdown messages"""
    while True:
        try:
            await update_countdown_messages()
        except Exception as e:
            print(f"Error in countdown loop: {e}")
        
        await asyncio.sleep(COUNTDOWN_UPDATE_INTERVAL)

async def send_boss_notification(boss: WorldBoss, channel: discord.TextChannel, minutes_until: int):
    """Send a notification about an upcoming boss spawn"""
    embed = create_boss_embed(boss, minutes_until)
    message = await channel.send(embed=embed)
    
    # If this is a countdown notification (not the spawn notification)
    if minutes_until > 0:
        active_countdowns[message.id] = {
            'boss_id': boss.id,
            'channel_id': channel.id,
            'next_spawn': boss.next_spawn
        }
        
        # Start the countdown loop if not running
        if not hasattr(bot, 'countdown_task') or bot.countdown_task.done():
            bot.countdown_task = bot.loop.create_task(countdown_loop())
    
    return message

def create_boss_embed(boss: WorldBoss, minutes_until: int) -> discord.Embed:
    """Create an enhanced embed for boss notifications with premium styling"""
    now = datetime.now(IRAN_TIMEZONE)

    # Get boss theme and notification style
    boss_theme = get_boss_theme(boss.id)

    # Determine notification type and style
    if minutes_until <= 0:
        notification_type = "spawn"
        urgency_level = "spawn"
    elif minutes_until <= 5:
        notification_type = "5_min"
        urgency_level = "urgent"
    elif minutes_until <= 10:
        notification_type = "10_min"
        urgency_level = "warning"
    else:
        notification_type = "info"
        urgency_level = "info"

    notification_style = get_notification_style(notification_type)

    # Create enhanced title
    title = create_boss_title(boss, minutes_until, notification_style, boss_theme)

    # Use boss-specific color or notification color
    embed_color = boss_theme.get("color", notification_style.get("color", 0x808080))

    # Create the embed with enhanced styling
    embed = discord.Embed(title=title, color=embed_color)

    # Add visual separator
    separator_style = "dragons" if "dragon" in boss_theme.get("type", "").lower() else "fancy"
    separator = create_visual_separator(separator_style)

    # Enhanced location field with themed emojis
    location_emojis = " ".join(boss_theme.get("theme_emojis", ["⚔️"])[:2])
    location_value = f"{location_emojis} **{boss.location}**\n🗺️ `{boss.waypoint}`"
    embed.add_field(name="📍 **LOCATION**", value=location_value, inline=True)

    # Boss type and element info
    boss_type = boss_theme.get("type", "Unknown")
    element_emoji = boss_theme.get("element", "⭐")
    type_value = f"{element_emoji} **{boss_type}**"
    embed.add_field(name="🏷️ **TYPE**", value=type_value, inline=True)

    # Add empty field for layout
    embed.add_field(name="\u200b", value="\u200b", inline=True)

    # Enhanced countdown/status section
    if minutes_until > 0 and boss.next_spawn:
        time_until = boss.next_spawn - now

        # Enhanced countdown display
        countdown_display = format_countdown_display(time_until, boss_theme, urgency_level)
        embed.add_field(name="⏳ **COUNTDOWN**", value=countdown_display, inline=False)

        # Spawn time with Discord timestamp
        next_spawn_str = f"<t:{int(boss.next_spawn.timestamp())}:t>"
        spawn_value = f"🕐 {next_spawn_str} **(IRST)**"
        embed.add_field(name="⏰ **SPAWN TIME**", value=spawn_value, inline=True)

        # Urgency indicator
        if urgency_level == "urgent":
            urgency_value = "🚨 **VERY SOON!** 🚨"
        elif urgency_level == "warning":
            urgency_value = "⚠️ **GET READY!** ⚠️"
        else:
            urgency_value = "📅 **SCHEDULED** 📅"
        embed.add_field(name="🎯 **STATUS**", value=urgency_value, inline=True)

    elif boss.next_spawn:
        # Boss is live
        live_emojis = " ".join(boss_theme.get("theme_emojis", ["⚔️"])[:3])
        status_value = f"🎯 **BOSS IS LIVE!** 🎯\n{live_emojis}\n✨ **Good luck, heroes!** ✨"
        embed.add_field(name="⚔️ **BATTLE STATUS**", value=status_value, inline=False)

    # Add visual separator before tips
    embed.add_field(name="\u200b", value=f"`{separator}`", inline=False)

    # Enhanced tips section
    tips = get_enhanced_boss_tips(boss.id, boss_theme)
    if tips:
        embed.add_field(name="💡 **STRATEGY TIPS**", value=tips, inline=False)

    # Enhanced footer with themed elements
    footer_emoji = boss_theme.get("emoji", "⚔️")
    footer_text = f"{footer_emoji} Guild Wars 2 World Boss Timer • Times in IRST {footer_emoji}"
    embed.set_footer(text=footer_text)

    # Add thumbnail for visual appeal (using boss emoji as text)
    if minutes_until <= 0:
        embed.add_field(name="🎉 **CELEBRATION**", value="🎊 The battle awaits! 🎊", inline=False)

    return embed

def get_enhanced_boss_tips(boss_id: str, boss_theme: Dict[str, Any]) -> str:
    """Get enhanced tips for a specific boss with themed emojis"""
    theme_emojis = boss_theme.get("theme_emojis", ["⚔️", "🎯", "💫", "✨"])

    enhanced_tips = {
        # Core Tyria Bosses
        "shatterer": (
            f"{theme_emojis[0]} **Crowd Control** - Bring CC for the breakbar\n"
            f"{theme_emojis[1]} **Positioning** - Stay spread during crystal phase\n"
            f"{theme_emojis[2]} **Awareness** - Watch for the tail swipe attack\n"
            f"⚡ **Element**: Crystal/Lightning damage"
        ),
        "tequatl": (
            f"{theme_emojis[0]} **Defense** - Protect the turrets at all costs\n"
            f"{theme_emojis[1]} **Speed** - Kill the hordes quickly\n"
            f"{theme_emojis[2]} **Coordination** - Stack on boss during burn phase\n"
            f"💀 **Element**: Undead/Poison damage"
        ),
        "claw_of_jormag": (
            f"{theme_emojis[0]} **Destruction** - Destroy the ice walls\n"
            f"{theme_emojis[1]} **Dodging** - Watch for the ice spikes\n"
            f"{theme_emojis[2]} **Cleansing** - Bring condition cleanses\n"
            f"❄️ **Element**: Ice/Chill damage"
        ),
        "megadestroyer": (
            f"{theme_emojis[0]} **Movement** - Avoid the lava pools\n"
            f"{theme_emojis[1]} **Breakbar** - Break the defiance bar quickly\n"
            f"{theme_emojis[2]} **Reflection** - Bring projectile reflects\n"
            f"🔥 **Element**: Fire/Burning damage"
        ),
        "golem_mark_ii": (
            f"{theme_emojis[0]} **Evasion** - Avoid the laser beams\n"
            f"{theme_emojis[1]} **Targets** - Destroy the power cores\n"
            f"{theme_emojis[2]} **Stability** - Bring stability for knockbacks\n"
            f"⚙️ **Element**: Mechanical/Electric damage"
        ),
        "fire_elemental": (
            f"{theme_emojis[0]} **Cleansing** - Bring condition cleanses for burning\n"
            f"{theme_emojis[1]} **Mobility** - Stay mobile to avoid fire patches\n"
            f"{theme_emojis[2]} **Range** - Ranged attacks work well\n"
            f"🔥 **Element**: Pure fire damage"
        ),
        "great_jungle_wurm": (
            f"{theme_emojis[0]} **Organization** - Split into three groups for heads\n"
            f"{theme_emojis[1]} **Awareness** - Watch for the poison fields\n"
            f"{theme_emojis[2]} **Cleansing** - Bring condition cleanses\n"
            f"🌿 **Element**: Nature/Poison damage"
        ),
        "shadow_behemoth": (
            f"{theme_emojis[0]} **Priority** - Close the portals quickly\n"
            f"{theme_emojis[1]} **Positioning** - Watch for the shadow rifts\n"
            f"{theme_emojis[2]} **Focus** - Target the hands when they appear\n"
            f"🌑 **Element**: Shadow/Dark damage"
        ),
        "svanir_shaman_chief": (
            f"{theme_emojis[0]} **Movement** - Stay mobile to avoid ice patches\n"
            f"{theme_emojis[1]} **Stability** - Bring stability for knockbacks\n"
            f"{theme_emojis[2]} **Priority** - Focus on the shaman first\n"
            f"❄️ **Element**: Ice/Frost damage"
        ),
        "taidha_covington": (
            f"{theme_emojis[0]} **Awareness** - Watch for the cannon barrage\n"
            f"{theme_emojis[1]} **Mobility** - Stay mobile to avoid AoE attacks\n"
            f"{theme_emojis[2]} **Cleansing** - Bring condition cleanses for bleeding\n"
            f"⚔️ **Element**: Physical/Bleeding damage"
        ),
        # HoT and PoF Bosses
        "chak_gerent": (
            f"{theme_emojis[0]} **Organization** - Split into four lanes\n"
            f"{theme_emojis[1]} **Breakbar** - CC when the breakbar appears\n"
            f"{theme_emojis[2]} **Hazards** - Watch for the Chak Acid\n"
            f"🟤 **Element**: Acid/Poison damage"
        ),
        "death_branded_shatterer": (
            f"{theme_emojis[0]} **Zones** - Avoid the Branded zones\n"
            f"{theme_emojis[1]} **Cleansing** - Bring condition cleanses\n"
            f"{theme_emojis[2]} **Awareness** - Watch for the tail swipe\n"
            f"🔮 **Element**: Branded/Crystal damage"
        ),
        "drakkar": (
            f"{theme_emojis[0]} **Breakbar** - Break the defiance bar quickly\n"
            f"{theme_emojis[1]} **Positioning** - Avoid the ice fields\n"
            f"{theme_emojis[2]} **Cleansing** - Bring condition cleanses for chill\n"
            f"🧊 **Element**: Elder ice damage"
        ),
        "dragonstorm": (
            f"{theme_emojis[0]} **Split** - Two groups for the dragons\n"
            f"{theme_emojis[1]} **Special** - Use the special action key\n"
            f"{theme_emojis[2]} **Mechanic** - Watch for shared agony\n"
            f"⚡ **Element**: Storm/Lightning damage"
        ),
        # EoD Bosses
        "soo_won": (
            f"{theme_emojis[0]} **Groups** - Split for tail and head\n"
            f"{theme_emojis[1]} **Technology** - Use jade bot protocols\n"
            f"{theme_emojis[2]} **Tsunami** - Watch for the tsunami mechanic\n"
            f"🌊 **Element**: Water/Tsunami damage"
        ),
        "aetherblade_assault": (
            f"{theme_emojis[0]} **Priority** - Focus on snipers first\n"
            f"{theme_emojis[1]} **Dodging** - Avoid the red circles\n"
            f"{theme_emojis[2]} **Stability** - Bring stability for knockbacks\n"
            f"⚡ **Element**: Aetherblade technology"
        ),
        # Other Notable Bosses
        "karka_queen": (
            f"{theme_emojis[0]} **Movement** - Avoid the rolling karka\n"
            f"{theme_emojis[1]} **Awareness** - Watch for the acid spray\n"
            f"{theme_emojis[2]} **Cleansing** - Bring condition cleanses for bleeding\n"
            f"🌊 **Element**: Acid/Bleeding damage"
        ),
        "marionette": (
            f"{theme_emojis[0]} **Organization** - Five groups for platforms\n"
            f"{theme_emojis[1]} **Speed** - CC the champions quickly\n"
            f"{theme_emojis[2]} **Awareness** - Watch for the laser beams\n"
            f"⚙️ **Element**: Mechanical/Laser damage"
        ),
        "triple_trouble": (
            f"{theme_emojis[0]} **Coordination** - Multiple squads required\n"
            f"{theme_emojis[1]} **Mechanics** - Each wurm head is different\n"
            f"{theme_emojis[2]} **Targets** - Watch for egg sacs and husks\n"
            f"💀 **Element**: Poison/Nature damage"
        )
    }

    default_tip = (
        f"{theme_emojis[0]} **Teamwork** - Stay alert and work together!\n"
        f"{theme_emojis[1]} **Preparation** - Bring appropriate boons and conditions\n"
        f"{theme_emojis[2]} **Communication** - Watch for mechanics and communicate\n"
        f"⭐ **Element**: Various damage types"
    )

    return enhanced_tips.get(boss_id, default_tip)

def get_boss_tips(boss_id: str) -> str:
    """Get basic tips for a specific boss (legacy function)"""
    boss_theme = get_boss_theme(boss_id)
    return get_enhanced_boss_tips(boss_id, boss_theme)

@bot.event
async def on_ready():
    """Bot startup event with enhanced status messages"""
    current_time = datetime.now(IRAN_TIMEZONE).strftime("%Y-%m-%d %H:%M:%S IRST")

    print("=" * 60)
    print("🤖 GUILD WARS 2 WORLD BOSS BOT STARTING UP")
    print("=" * 60)
    print(f"📅 Startup Time: {current_time}")
    print(f"🔗 Bot Name: {bot.user.name}")
    print(f"🆔 Bot ID: {bot.user.id}")
    print(f"🌐 Connected to {len(bot.guilds)} guild(s)")

    # Handle command cleanup and syncing
    cleanup_commands = os.getenv('CLEANUP_COMMANDS', 'false').lower() == 'true'

    if cleanup_commands:
        print("🧹 CLEANUP_COMMANDS=true detected - Performing NUCLEAR command cleanup...")
        print("💥 This will remove ALL commands from ALL locations!")
        try:
            # Step 1: Clear ALL global commands
            print("🗑️  STEP 1: Clearing ALL GLOBAL slash commands...")
            await bot.sync_commands(commands=[])
            print("✅ Global commands cleared")

            # Step 2: Skip guild-specific clearing (will use HTTP API)
            print("🗑️  STEP 2: Skipping guild sync (will use HTTP API instead)")

            # Step 3: Force clear using HTTP API directly (more aggressive)
            print("🗑️  STEP 3: Force clearing via HTTP API...")
            try:
                # Clear global commands via HTTP
                await bot.http.bulk_upsert_global_commands(bot.application_id, [])
                print("   ✅ HTTP global commands cleared")

                # Clear guild commands via HTTP for each guild
                for guild in bot.guilds:
                    try:
                        await bot.http.bulk_upsert_guild_commands(bot.application_id, guild.id, [])
                        print(f"   ✅ HTTP guild commands cleared: {guild.name}")
                    except Exception as http_guild_error:
                        print(f"   ⚠️  HTTP guild clear failed {guild.name}: {http_guild_error}")

            except Exception as http_error:
                print(f"   ⚠️  HTTP clearing failed: {http_error}")

            # Step 4: Extended wait for Discord to process
            print("⏳ STEP 4: Extended wait for Discord processing...")
            await asyncio.sleep(10)

            # Step 5: Register ONLY our 3 new commands
            print("🔄 STEP 5: Registering ONLY our 3 new commands...")

            # Force sync with our current commands (they should be auto-detected)
            synced = await bot.sync_commands(force=True)

            # Check what got synced
            if synced:
                print(f"✅ Successfully registered {len(synced)} command(s)")
                for cmd in synced:
                    print(f"   • /{cmd.name}: {cmd.description}")
            else:
                print("⚠️  No commands were returned from sync")

            # Also check pending commands
            registered_commands = bot.pending_application_commands
            if registered_commands:
                print(f"📋 Pending commands: {len(registered_commands)}")
                for cmd in registered_commands:
                    print(f"   • /{cmd.name}: {cmd.description}")

            # Step 6: Skip guild syncing for now (global commands should work)
            print("🔄 STEP 6: Skipping guild sync (using global commands)")
            print("   ℹ️  Global commands should be available in all guilds")

            print("💡 TIP: Set CLEANUP_COMMANDS=false in .env after confirming commands are clean")
            print("🎉 NUCLEAR command cleanup complete!")
            print("🔍 Check Discord - you should now see ONLY 3 commands: /help, /boss_list, /next_boss")

        except Exception as e:
            print(f"❌ NUCLEAR cleanup failed: {e}")
            print("⚠️  Bot will continue running, but commands may not work properly")
    else:
        print("🔄 Syncing slash commands (normal mode)...")
        try:
            synced = await bot.sync_commands()
            if synced:
                print(f"✅ Successfully synced {len(synced)} command(s)")
                for cmd in synced:
                    print(f"   • /{cmd.name}: {cmd.description}")
            else:
                print("✅ Commands are already up to date")
        except Exception as e:
            print(f"❌ Failed to sync commands: {e}")
            print("⚠️  Bot will continue running, but commands may not work properly")

    # Start the boss timer loop and countdown loop
    print("⏰ Starting boss timer monitoring...")
    bot.loop.create_task(boss_timer_loop())

    # Start countdown loop if not already running
    if not hasattr(bot, 'countdown_task') or bot.countdown_task.done():
        bot.countdown_task = bot.loop.create_task(countdown_loop())

    print("🎯 Boss notification system active")
    print("=" * 60)
    print("✅ BOT IS NOW ONLINE AND READY!")
    print("=" * 60)

async def boss_timer_loop():
    """Main loop for checking boss timers"""
    await bot.wait_until_ready()
    
    while not bot.is_closed():
        try:
            await check_boss_timers()
        except Exception as e:
            print(f"Error in boss timer loop: {e}")
        
        await asyncio.sleep(30)  # Check every 30 seconds

@bot.slash_command(name="help", description="Show available commands and bot information")
async def help_command(ctx):
    """Display enhanced help information about the bot and its commands"""

    try:
        # Defer the response to prevent timeout
        await ctx.defer()

        # Create premium styled help embed
        embed = discord.Embed(
            title="🐉 **GUILD WARS 2 WORLD BOSS TIMER** 🐉",
            description="✨ *Your premium companion for tracking Tyria's most dangerous foes!* ✨",
            color=0x9932CC
        )

        # Add fancy separator
        separator = create_visual_separator("dragons")
        embed.add_field(name="\u200b", value=f"`{separator}`", inline=False)

        # Enhanced commands section
        commands_value = (
        "🆘 `/help` - Display this comprehensive guide\n"
        "🎯 `/next_boss <name>` - Get detailed boss spawn information\n"
        "📜 `/boss_list` - Browse all trackable world bosses\n"
        "\n💡 **Pro Tip**: Use partial boss names! Try `/next_boss tequatl`"
    )
    embed.add_field(
        name="⚔️ **AVAILABLE COMMANDS**",
        value=commands_value,
        inline=False
    )

    # Enhanced notifications section
    notifications_value = (
        "🚨 **10-minute warning** - Get ready and gather your squad!\n"
        "⚠️ **5-minute warning** - Final preparations and positioning\n"
        "🎯 **Spawn alert** - Boss is live with celebration message!\n"
        "⏳ **Live countdowns** - Real-time progress bars and timers\n"
        "🎨 **Themed visuals** - Each boss has unique styling and emojis"
    )
    embed.add_field(
        name="🔔 **AUTOMATIC NOTIFICATIONS**",
        value=notifications_value,
        inline=False
    )

    # Enhanced boss coverage
    bosses_value = (
        "🏔️ **Core Tyria** - Shatterer, Tequatl, Claw of Jormag, and more\n"
        "🌟 **Heart of Thorns** - Chak Gerent and jungle champions\n"
        "🏜️ **Path of Fire** - Desert and Branded threats\n"
        "🌊 **End of Dragons** - Soo-Won and Aetherblade forces\n"
        "🎪 **Special Events** - Marionette, Triple Trouble, and others"
    )
    embed.add_field(
        name="🌍 **SUPPORTED CONTENT**",
        value=bosses_value,
        inline=False
    )

    # Features section
    features_value = (
        "⏰ **IRST Timezone** - All times in Iran Standard Time\n"
        "🎨 **Premium Styling** - Beautiful embeds with boss-specific themes\n"
        "📊 **Smart Progress Bars** - Visual countdown with themed elements\n"
        "💡 **Strategy Tips** - Detailed tactics for each encounter\n"
        "🔄 **Auto-Updates** - Live countdown refreshes every 30 seconds"
    )
        embed.add_field(
            name="✨ **PREMIUM FEATURES**",
            value=features_value,
            inline=False
        )

        # Add another separator
        embed.add_field(name="\u200b", value=f"`{separator}`", inline=False)

        # Enhanced footer
        embed.set_footer(
            text="🐉 Created for Guild Wars 2 Heroes • Powered by Premium Bot Technology 🐉",
            icon_url=None
        )

        # Send the response
        await ctx.followup.send(embed=embed)

    except Exception as e:
        # Error handling
        error_embed = discord.Embed(
            title="❌ **ERROR**",
            description=f"Failed to load help information: {str(e)}",
            color=0xFF0000
        )
        try:
            if ctx.response.is_done():
                await ctx.followup.send(embed=error_embed)
            else:
                await ctx.respond(embed=error_embed)
        except:
            print(f"Failed to send help error message: {e}")

@bot.slash_command(name="boss_list", description="Show all available world bosses")
async def boss_list(ctx):
    """Display an enhanced list of all trackable world bosses"""

    try:
        # Defer the response to prevent timeout
        await ctx.defer()

        # Create premium styled boss list embed
        embed = discord.Embed(
            title="📜 **TYRIA'S MOST WANTED** 📜",
            description="🗡️ *Complete roster of trackable world bosses* 🗡️",
            color=0x8B0000
        )

        # Group bosses by category with compact formatting
        core_bosses = []
        expansion_bosses = []
        special_bosses = []

        for boss_id, boss_data in WORLD_BOSSES.items():
            boss_name = boss_data["name"]
            location = boss_data["location"]
            boss_theme = get_boss_theme(boss_id)
            boss_emoji = boss_theme.get("emoji", "⚔️")

            # More compact format to avoid embed size limits
            boss_entry = f"{boss_emoji} **{boss_name}** - *{location}*"

            if boss_id in ["shatterer", "tequatl", "claw_of_jormag", "megadestroyer",
                           "golem_mark_ii", "fire_elemental", "great_jungle_wurm",
                           "shadow_behemoth", "svanir_shaman_chief", "taidha_covington"]:
                core_bosses.append(boss_entry)
            elif boss_id in ["chak_gerent", "death_branded_shatterer", "drakkar",
                             "dragonstorm", "soo_won", "aetherblade_assault"]:
                expansion_bosses.append(boss_entry)
            else:
                special_bosses.append(boss_entry)

        # Add core bosses (limit to prevent embed size issues)
        if core_bosses:
            embed.add_field(
                name="🏔️ **CORE TYRIA LEGENDS**",
                value="\n".join(core_bosses[:6]),  # Reduced limit
                inline=False
            )

        # Add expansion bosses
        if expansion_bosses:
            embed.add_field(
                name="🌟 **EXPANSION CHAMPIONS**",
                value="\n".join(expansion_bosses),
                inline=False
            )

        # Add special bosses
        if special_bosses:
            embed.add_field(
                name="🎪 **SPECIAL EVENT BOSSES**",
                value="\n".join(special_bosses),
                inline=False
            )

        # Enhanced usage examples (compact)
        usage_value = (
            "🎯 `/next_boss tequatl` - Check spawn time\n"
            "🐉 `/next_boss shatterer` - Get boss info\n"
            "💡 **Tip**: Partial names work! Try `/next_boss claw`"
        )
        embed.add_field(
            name="📖 **USAGE**",
            value=usage_value,
            inline=False
        )

        # Enhanced footer
        total_bosses = len(WORLD_BOSSES)
        embed.set_footer(
            text=f"⚔️ {total_bosses} Legendary Foes • Ready for Battle! ⚔️"
        )

        # Send the response
        await ctx.followup.send(embed=embed)

    except Exception as e:
        # Error handling
        error_embed = discord.Embed(
            title="❌ **ERROR**",
            description=f"Failed to load boss list: {str(e)}",
            color=0xFF0000
        )
        try:
            if ctx.response.is_done():
                await ctx.followup.send(embed=error_embed)
            else:
                await ctx.respond(embed=error_embed)
        except:
            print(f"Failed to send error message: {e}")

@bot.slash_command(name="next_boss", description="Check when a world boss will spawn next")
async def next_boss(ctx, boss_name: str):
    """Get information about when a boss will spawn next"""
    boss_name = boss_name.lower().replace(" ", "_").replace("-", "_")

    if boss_name not in WORLD_BOSSES:
        # Try to find a partial match
        matches = [boss for boss in WORLD_BOSSES.keys() if boss_name in boss or boss in boss_name]
        if matches:
            boss_name = matches[0]
        else:
            # Enhanced error message
            embed = discord.Embed(
                title="🔍 **BOSS NOT FOUND** 🔍",
                description=f"❌ Could not locate: `{boss_name}`",
                color=0xFF4500
            )

            # Add visual separator
            separator = create_visual_separator("swords")
            embed.add_field(name="\u200b", value=f"`{separator}`", inline=False)

            # Enhanced suggestions
            suggestions_value = (
                "📜 Use `/boss_list` to browse all available bosses\n"
                "🎯 Try partial names like `tequatl`, `shatterer`, or `claw`\n"
                "💡 Boss names are case-insensitive!\n"
                "\n**Popular Bosses:**\n"
                "🐲 `tequatl` - Tequatl the Sunless\n"
                "🐉 `shatterer` - The Shatterer\n"
                "❄️ `claw` - Claw of Jormag"
            )
            embed.add_field(
                name="💡 **HELPFUL SUGGESTIONS**",
                value=suggestions_value,
                inline=False
            )

            embed.set_footer(text="🗡️ Keep hunting, hero! The right boss name awaits! 🗡️")
            await ctx.respond(embed=embed)
            return

    boss_data = WORLD_BOSSES[boss_name]
    boss = boss_cache.get(boss_name)

    if not boss or not boss.next_spawn:
        next_spawn = calculate_next_spawn(boss_name)
        if not boss:
            boss = WorldBoss(
                boss_name,
                boss_data["name"],
                boss_data["waypoint"],
                boss_data["location"],
                boss_data["event_id"]
            )
            boss_cache[boss_name] = boss
        boss.update_spawn_time(next_spawn)

    embed = create_boss_embed(boss, int((boss.next_spawn - datetime.now(IRAN_TIMEZONE)).total_seconds() / 60))
    await ctx.respond(embed=embed)

def signal_handler(signum=None, frame=None):
    """Handle shutdown signals gracefully"""
    current_time = datetime.now(IRAN_TIMEZONE).strftime("%Y-%m-%d %H:%M:%S IRST")
    print("\n" + "=" * 60)
    print("🛑 SHUTDOWN SIGNAL RECEIVED")
    print("=" * 60)
    print(f"📅 Shutdown Time: {current_time}")
    print("🔄 Gracefully stopping bot...")
    print("💾 Cleaning up resources...")

    # Clean up active countdowns
    if active_countdowns:
        print(f"🧹 Cleaning up {len(active_countdowns)} active countdown(s)")
        active_countdowns.clear()

    # Cancel background tasks
    if hasattr(bot, 'countdown_task') and not bot.countdown_task.done():
        bot.countdown_task.cancel()
        print("⏹️ Countdown task cancelled")

    print("=" * 60)
    print("✅ BOT SHUTDOWN COMPLETE - GOODBYE!")
    print("=" * 60)

    # Exit gracefully
    sys.exit(0)

# Run the bot
if __name__ == "__main__":
    # Set up signal handlers for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)  # Ctrl+C
    signal.signal(signal.SIGTERM, signal_handler)  # Termination signal

    try:
        print("🚀 Starting Guild Wars 2 World Boss Bot...")
        print("⚡ Press Ctrl+C to stop the bot gracefully")
        print("-" * 60)

        # Get token and validate
        token = os.getenv('DISCORD_TOKEN')
        if not token:
            print("❌ ERROR: DISCORD_TOKEN not found in environment variables!")
            print("💡 Make sure your .env file contains: DISCORD_TOKEN=your_bot_token")
            sys.exit(1)

        # Run the bot
        bot.run(token)

    except KeyboardInterrupt:
        signal_handler(signal.SIGINT, None)
    except Exception as e:
        current_time = datetime.now(IRAN_TIMEZONE).strftime("%Y-%m-%d %H:%M:%S IRST")
        print("\n" + "=" * 60)
        print("💥 UNEXPECTED ERROR OCCURRED")
        print("=" * 60)
        print(f"📅 Error Time: {current_time}")
        print(f"❌ Error: {e}")
        print("🔄 Bot will attempt to restart...")
        print("=" * 60)
        sys.exit(1)
